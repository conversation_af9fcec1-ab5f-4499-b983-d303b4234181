#!/usr/bin/env python3
"""
Script to process Consent_Addendum.csv and add Copy_Audio and Copy_Demographics columns
based on consent text patterns.
"""

import pandas as pd
import os

def process_consent_csv(input_file='Consent_Addendum_8-18-25.csv', output_file='Consent_Addendum_8-18-25.csv'):
    """
    Process the consent CSV file and add Copy_Audio and Copy_Demographics columns.
    
    Args:
        input_file (str): Path to input CSV file
        output_file (str): Path to output CSV file
    """
    
    # Read the CSV file
    try:
        df = pd.read_csv(input_file)
        print(f"Successfully read {input_file}")
        print(f"Original shape: {df.shape}")
        print(f"Columns: {df.columns.tolist()}")
    except FileNotFoundError:
        print(f"Error: File {input_file} not found")
        return
    except Exception as e:
        print(f"Error reading file: {e}")
        return
    
    # Initialize new columns
    df['Copy_Audio'] = ''
    df['Copy_Demographics'] = ''
    
    # Process each row based on consent text
    for index, row in df.iterrows():
        consent_text = row['Consent'].strip()
        
        # Rule 1: Only deidentified audio can be shared for commercial purposes
        if consent_text == "I agree that only the deidentified audio can be shared for commercial purposes.":
            df.at[index, 'Copy_Audio'] = 'yes'
            df.at[index, 'Copy_Demographics'] = 'no'
        
        # Rule 2: Audio and demographic information can be shared OR educational tools agreement
        elif (consent_text == "I agree that the deidentified audio and basic demographic information (age, sex assigned at birth, race, speech development stage, household income range) can be shared and used for commercial purposes." or
              consent_text == "Yes, I understand and agree that third parties, including commercial entities, may access and use the data for commercial purposes to develop child educational tools . The products will be for child educational tool development only. Educational is defined as academic, therapeutic, or child entertainment products."):
            df.at[index, 'Copy_Audio'] = 'yes'
            df.at[index, 'Copy_Demographics'] = 'yes'
        
        # Rule 3: No agreement for commercial purposes
        elif consent_text == "No, I do not agree with allowing third parties to use any data for commercial purposes.":
            df.at[index, 'Copy_Audio'] = 'no'
            df.at[index, 'Copy_Demographics'] = 'no'
        
        # Handle any unexpected consent text
        else:
            print(f"Warning: Unexpected consent text at row {index + 1}: {consent_text}")
            df.at[index, 'Copy_Audio'] = 'unknown'
            df.at[index, 'Copy_Demographics'] = 'unknown'
    
    # Display summary statistics
    print("\nProcessing Summary:")
    print(f"Total rows processed: {len(df)}")
    print("\nCopy_Audio distribution:")
    print(df['Copy_Audio'].value_counts())
    print("\nCopy_Demographics distribution:")
    print(df['Copy_Demographics'].value_counts())
    
    # Save the processed file
    try:
        df.to_csv(output_file, index=False)
        print(f"\nProcessed file saved as: {output_file}")
    except Exception as e:
        print(f"Error saving file: {e}")
        return
    
    # Display first few rows of the result
    print("\nFirst 5 rows of processed data:")
    print(df[['RecordID', 'ParticipantID', 'Copy_Audio', 'Copy_Demographics']].head())
    
    return df

def main():
    """Main function to run the script."""
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("Processing Consent CSV file...")
    print("=" * 50)
    
    # Process the file
    processed_df = process_consent_csv()
    
    if processed_df is not None:
        print("\nProcessing completed successfully!")
    else:
        print("\nProcessing failed!")

if __name__ == "__main__":
    main()
