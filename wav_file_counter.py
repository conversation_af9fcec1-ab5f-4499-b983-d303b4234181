#!/usr/bin/env python3
"""
WAV File Counter Script

This script analyzes WAV files in subdirectories that start with 'sub-' and generates
a CSV report with participant IDs and task counts. Only counts files from participants
who have consent permissions in the consent sheet.

Usage:
    python3 wav_file_counter.py <root_directory> [output_csv]

Arguments:
    root_directory: Path to the root directory containing sub- folders
    output_csv: Optional output CSV filename (default: wav_file_analysis.csv)
"""

import os
import sys
import csv
import re
import pandas as pd
from pathlib import Path
from collections import defaultdict
import argparse


def get_consented_participants(consent_file='Consent_Addendum_8-7-25.csv'):
    """
    Read consent file and return participants with audio consent permissions.
    
    Args:
        consent_file (str): Path to consent CSV file
        
    Returns:
        set: Set of ParticipantIDs with Copy_Audio = yes
    """
    try:
        df = pd.read_csv(consent_file)
        # Get participants with Copy_Audio = yes
        consented_participants = set(df[df['Copy_Audio'].str.lower() == 'yes']['ParticipantID'])
        
        print(f"Loaded consent data for {len(df)} participants")
        print(f"Participants with Copy_Audio = yes: {len(consented_participants)}")
        
        return consented_participants
    except Exception as e:
        print(f"Error reading consent file: {e}")
        return set()


def extract_participant_id(filepath):
    """
    Extract participant ID from filepath by finding folder names that start with 'sub-'
    
    Args:
        filepath (str): Full path to the file
        
    Returns:
        str: Participant ID (the sub- folder name) or None if not found
    """
    path_parts = Path(filepath).parts
    for part in path_parts:
        if part.startswith('sub-'):
            return part
    return None


def extract_task_code(filename):
    """
    Extract task code from filename using pattern _task-<taskcode>
    
    Args:
        filename (str): Name of the file
        
    Returns:
        str: Task code or None if not found
    """
    # Pattern to match _task-<taskcode>
    pattern = r'_task-([A-Za-z0-9]+)'
    match = re.search(pattern, filename)
    if match:
        return match.group(1).upper()  # Convert to uppercase for consistency
    return None


def find_wav_files(root_directory, consented_participants=None):
    """
    Find all WAV files in subdirectories that start with 'sub-' for consented participants only
    
    Args:
        root_directory (str): Root directory to search
        consented_participants (set): Set of participant IDs with consent (if None, include all)
        
    Returns:
        list: List of tuples (filepath, filename) for all WAV files found from consented participants
    """
    wav_files = []
    root_path = Path(root_directory)
    
    if not root_path.exists():
        print(f"Error: Directory '{root_directory}' does not exist.")
        return wav_files
    
    total_folders = 0
    consented_folders = 0
    skipped_folders = 0
    
    # Iterate through immediate subdirectories
    for item in root_path.iterdir():
        if item.is_dir() and item.name.startswith('sub-'):
            total_folders += 1
            
            # Check if this participant has consent (if consent list provided)
            if consented_participants is not None:
                participant_has_consent = False
                for participant_id in consented_participants:
                    if participant_id in item.name:
                        participant_has_consent = True
                        break
                
                if not participant_has_consent:
                    skipped_folders += 1
                    print(f"Skipped directory: {item.name} (participant not in consent sheet)")
                    continue
            
            consented_folders += 1
            print(f"Scanning directory: {item.name}")
            
            # Recursively find all .wav files in this subdirectory
            folder_wav_count = 0
            for wav_file in item.rglob('*.wav'):
                wav_files.append((str(wav_file), wav_file.name))
                folder_wav_count += 1
            
            print(f"  Found {folder_wav_count} wav files in {item.name}")
    
    print(f"\nFolder summary:")
    print(f"Total sub-* folders found: {total_folders}")
    print(f"Folders with consented participants: {consented_folders}")
    print(f"Folders skipped (no consent): {skipped_folders}")
    print(f"Total wav files from consented participants: {len(wav_files)}")
    
    return wav_files


def analyze_files(root_directory, consented_participants=None):
    """
    Analyze WAV files and count them by participant and task for consented participants only
    
    Args:
        root_directory (str): Root directory to analyze
        consented_participants (set): Set of participant IDs with consent
        
    Returns:
        dict: Nested dictionary with participant IDs as keys and task counts as values
    """
    # Define the expected task codes
    expected_tasks = {'TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT'}
    
    # Initialize data structure
    participant_data = defaultdict(lambda: defaultdict(int))
    
    # Find all WAV files (only from consented participants)
    wav_files = find_wav_files(root_directory, consented_participants)
    
    if not wav_files:
        print("No WAV files found in subdirectories from consented participants")
        return participant_data
    
    print(f"\nProcessing {len(wav_files)} WAV files from consented participants")
    
    # Process each WAV file
    for filepath, filename in wav_files:
        # Extract participant ID
        participant_id = extract_participant_id(filepath)
        if not participant_id:
            print(f"Warning: Could not extract participant ID from {filepath}")
            continue
        
        # Extract task code
        task_code = extract_task_code(filename)
        if not task_code:
            print(f"Warning: Could not extract task code from {filename}")
            continue
        
        # Categorize task code
        if task_code in expected_tasks:
            participant_data[participant_id][task_code] += 1
        else:
            participant_data[participant_id]['Other'] += 1
        
        print(f"  {participant_id}: {task_code} -> {filename}")
    
    return participant_data


def generate_csv(participant_data, output_file):
    """
    Generate CSV output with participant data
    
    Args:
        participant_data (dict): Data structure with participant and task counts
        output_file (str): Output CSV filename
    """
    # Define column headers
    task_columns = ['TPL', 'PCT', 'GFTA', 'PLSS', 'CELFS', 'CELFT', 'Other']
    headers = ['ParticipantID'] + task_columns
    
    # Sort participants for consistent output
    sorted_participants = sorted(participant_data.keys())
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(headers)
            
            # Write data rows
            for participant_id in sorted_participants:
                row = [participant_id]
                for task in task_columns:
                    count = participant_data[participant_id].get(task, 0)
                    row.append(count)
                writer.writerow(row)
        
        print(f"\nCSV report generated: {output_file}")
        print(f"Total participants: {len(sorted_participants)}")
        
        # Print summary
        print("\nSummary:")
        for participant_id in sorted_participants:
            total_files = sum(participant_data[participant_id].values())
            print(f"  {participant_id}: {total_files} files")
            
    except Exception as e:
        print(f"Error writing CSV file: {e}")


def main():
    """Main function to run the analysis"""
    parser = argparse.ArgumentParser(
        description='Analyze WAV files in sub- directories for consented participants and generate CSV report'
    )
    parser.add_argument(
        'root_directory',
        help='Root directory containing sub- folders'
    )
    parser.add_argument(
        'output_csv',
        nargs='?',
        default='wav_file_analysis.csv',
        help='Output CSV filename (default: wav_file_analysis.csv)'
    )
    parser.add_argument(
        '--consent-file',
        default='Consent_Addendum_8-7-25.csv',
        help='Path to consent CSV file (default: Consent_Addendum_8-7-25.csv)'
    )
    parser.add_argument(
        '--no-consent-filter',
        action='store_true',
        help='Include all participants regardless of consent status'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    args = parser.parse_args()
    
    # Validate input directory
    if not os.path.exists(args.root_directory):
        print(f"Error: Directory '{args.root_directory}' does not exist.")
        sys.exit(1)
    
    if not os.path.isdir(args.root_directory):
        print(f"Error: '{args.root_directory}' is not a directory.")
        sys.exit(1)
    
    print(f"WAV File Counter (Consent-Aware)")
    print("=" * 50)
    print(f"Analyzing WAV files in: {args.root_directory}")
    print(f"Output CSV: {args.output_csv}")
    print(f"Consent file: {args.consent_file}")
    print(f"Consent filtering: {'Disabled' if args.no_consent_filter else 'Enabled'}")
    print("-" * 50)
    
    # Get consented participants (unless disabled)
    consented_participants = None
    if not args.no_consent_filter:
        consented_participants = get_consented_participants(args.consent_file)
        if not consented_participants:
            print("Warning: No consented participants found. Use --no-consent-filter to include all.")
            print("Continuing with consent filtering disabled...")
            consented_participants = None
    
    # Analyze files
    participant_data = analyze_files(args.root_directory, consented_participants)
    
    if not participant_data:
        print("No data found. Please check that:")
        print("1. The root directory contains subdirectories starting with 'sub-'")
        print("2. Those subdirectories contain WAV files")
        print("3. The WAV files have task codes in format '_task-<taskcode>'")
        if not args.no_consent_filter:
            print("4. The participants have consent permissions in the consent file")
        sys.exit(1)
    
    # Generate CSV
    generate_csv(participant_data, args.output_csv)


if __name__ == "__main__":
    main()
